# app/data_retriever.py
import logging
import json

from app.pdf_processor import process_pdf_new
from services.response_service import (generate_response_content_admin, generate_response_chat, generate_response_img,
                                       generate_mcq_explanation, handle_tts_data, generate_mcq_img_response)
from utils.pinecone_utils import (get_vectorstore, retrieve_text_chunks_from_redis, retrieve_data)
from config import INDEX_NAME_USER, INDEX_NAME_ADMIN, INDEX_NAME_DRIVE
from utils.redis_util import get_redis_client

logger = logging.getLogger(__name__)

# Connect to Redis server
r = get_redis_client()


async def generate_response(prompt, namespace, res_type, chat_history, chat_history_app, user_type, custom_prompt, for_book):
    """
    Function to generate a response based on a given request.
    It retrieves data from Pinecone and generates the response content.

    Args:
        chat_history_app: AI Chat History for app
        chat_history: AI Chat History for web
        res_type: The type of resource which the response is for
        namespace: The namespace to retrieve data from
        prompt: The query string
        user_type: The type of user
        custom_prompt
        for_book

    Returns:
        dict: The response from the data retrieval operation.
    """

    logger.info(f"Inside generate_response")
    logger.info(f"Prompt: {prompt[:10]}")
    logger.info(f"Namespace: {namespace}")
    logger.info(f"Res Type: {res_type}")
    logger.info(f"User Type: {user_type}")
    logger.info(f"Custom Prompt: {custom_prompt[:10]}")
    logger.info(f"Chat History: {chat_history}")

    index = None
    if user_type.lower() == "user":
        index = INDEX_NAME_USER
    elif user_type.lower() == "drive":
        index = INDEX_NAME_DRIVE

    temp_ns = namespace
    split_values = namespace.split('_')
    count_values = len(split_values)
    if count_values == 3:
        index = split_values[0]
        namespace = split_values[1] + "_" + split_values[2]

    # Retrieve data from Pinecone
    vector_store = await get_vectorstore(namespace, index, user_type)

    if chat_history:
        response_content = generate_response_chat(prompt, vector_store, chat_history, custom_prompt, for_book)
    elif chat_history_app:
        if isinstance(chat_history_app, str):
            chat_history_app = json.loads(chat_history_app)
        response_content = generate_response_chat(prompt, vector_store, chat_history_app, custom_prompt, for_book)
    else:
        response_content = generate_response_chat(prompt, vector_store, [], custom_prompt, for_book)

    # Create the API response
    response = {
        "answer": response_content,
        "namespace": temp_ns,
        "index": index,
        "query": prompt,
        "resType": res_type,
        "status_code": 200
    }

    return response


def get_vector_data_for_admin(namespace):
    """
    Function to get vector data from Redis

    Args:
        namespace (str): The namespace to retrieve data from.

    Returns:
        dict: The vector data.
    """

    vector_data = retrieve_text_chunks_from_redis(namespace)
    return vector_data


async def generate_response_admin_new(request):
    """
    Function to generate a response for admin based on a given request.
    It retrieves data from Pinecone and generates the response content.

    Args:
        request (object): The request object containing the query parameters.
        request.query (str): The query string.
        request.namespace (str): The namespace to retrieve data from.
        request.resType (str): The type of resource which the response is for.
        request.username (str): The username of the admin.

    Returns:
        dict: The response from the data retrieval operation.
    """
    prompt = request.query
    namespace = request.namespace
    res_type = request.resType
    username = request.username
    file_path = request.filePath
    reading_material_res_id = request.readingMaterialResId

    namespace_present = check_namespace_in_redis(namespace)

    res_id = get_res_id_from_ns(namespace)

    if namespace_present:
        vector_data = get_vector_data_for_admin(namespace)
    else:
        await process_pdf_new(file_path, namespace, INDEX_NAME_ADMIN, "Admin", res_id)
        vector_data = get_vector_data_for_admin(namespace)

    response_content, cost_summary, token_summary, concatenated_answers = generate_response_content_admin("admin", prompt,
                                                                                                          res_type,
                                                                                                          vector_data)

    # Create the API response
    response = {
        "answer": concatenated_answers,
        "namespace": namespace,
        "query": prompt,
        "resType": res_type,
        "username": username,
        "readingMaterialResId": reading_material_res_id,
        "inputCost": cost_summary['totalInputCost'],
        "outputCost": cost_summary['totalOutputCost'],
        "totalCost": cost_summary['totalTotalCost'],
        "promptTokens": token_summary['totalPromptTokens'],
        "completionTokens": token_summary['totalCompletionTokens'],
        "totalTokens": token_summary['totalTotalTokens'],
        "answerCharCount": len(concatenated_answers),
        "status_code": 200
    }
    return response


def get_or_store_vector_data(reading_material_res_id, prompt, namespace, index, user_type):
    """
    Function to get vector data from Redis or store it if not present.

    Args:
        reading_material_res_id (str): The ID for the reading material.
        prompt (str): The query string.
        namespace (str): The namespace to retrieve data from.
        index (str): The name of the index.
        user_type (str): The name of the index.

    Returns:
        dict: The vector data.
    """
    if not reading_material_res_id:
        raise ValueError("reading_material_res_id cannot be empty or None")

    # Check if vector data is already present in Redis

    cached_vector_data = get_vector_data_for_admin(namespace)

    if cached_vector_data:
        vector_data = json.loads(cached_vector_data)
    else:
        # Retrieve data from Pinecone
        vector_data = retrieve_data(prompt, namespace, index, user_type, True)
        # Store vector data in Redis with expiry time of 30 minutes (1800 seconds)
        r.set(reading_material_res_id, json.dumps(vector_data), ex=1800)
        vector_data = json.loads(r.get(reading_material_res_id))
        logger.info(f"Vector Data retrieved from Redis: {vector_data}")

    return vector_data

def generate_response_admin(request, user_type):
    """
    Function to generate a response for admin based on a given request.
    It retrieves data from Pinecone and generates the response content.

    Args:
        request (object): The request object containing the query parameters.
        request.query (str): The query string.
        request.namespace (str): The namespace to retrieve data from.
        request.resType (str): The type of resource which the response is for.
        request.username (str): The username of the admin.
        user_type

    Returns:
        dict: The response from the data retrieval operation.
    """

    prompt = request.query
    namespace = request.namespace
    res_type = request.resType
    username = request.username
    reading_material_res_id = request.readingMaterialResId
    index = INDEX_NAME_ADMIN

    temp_ns = namespace
    split_values = namespace.split('_')
    count_values = len(split_values)
    if count_values == 3:
        index = split_values[0]
        namespace = split_values[1] + "_" + split_values[2]

    # Get or store vector data
    vector_data = get_or_store_vector_data(reading_material_res_id, prompt, namespace, index, user_type)

    response_content, cost_summary, token_summary, concatenated_answers = generate_response_content_admin("admin", prompt,
                                                                                                          res_type,
                                                                                                          vector_data)

    # Create the API response
    response = {
        "answer": concatenated_answers,
        "namespace": temp_ns,
        "index": index,
        "query": prompt,
        "resType": res_type,
        "username": username,
        "readingMaterialResId": reading_material_res_id,
        "inputCost": cost_summary['totalInputCost'],
        "outputCost": cost_summary['totalOutputCost'],
        "totalCost": cost_summary['totalTotalCost'],
        "promptTokens": token_summary['totalPromptTokens'],
        "completionTokens": token_summary['totalCompletionTokens'],
        "totalTokens": token_summary['totalTotalTokens'],
        "answerCharCount": len(concatenated_answers),
        "status_code": 200
    }

    return response


def check_namespace_in_redis(namespace):
    # Check if any keys exist under the namespace
    pattern = f"{namespace}:*"
    keys = r.keys(pattern)

    if not keys:
        return False  # Namespace doesn't exist

    # If keys exist, check if there's data in them
    for key in keys:
        if r.hlen(key) > 0:
            return True  # Namespace exists and contains data

    return False  # Namespace exists but contains no data


def generate_ext_response(prompt, namespace, res_type, chat_history, user_type):
    """
    Function to generate a response based on a given request.
    It retrieves data from Pinecone and generates the response content.

    Args:
        chat_history: AI Chat History for web
        res_type: The type of resource which the response is for
        namespace: The namespace to retrieve data from
        prompt: The query string
        user_type: The type of user

    Returns:
        dict: The response from the data retrieval operation.
    """

    # Retrieve data from Pinecone
    namespace_present = check_namespace_in_redis(namespace)
    response_content = ''
    has_response = False
    if namespace_present:
        # Get vector data
        logger.info(f"Namespace present in Redis")
        vector_data = get_vector_data_for_admin(namespace)
        response_content = generate_response_content_admin("drive", prompt, res_type, vector_data)
        has_response = True
    # Create the API response
    response = {
        "answer": response_content[0],
        "namespace": namespace,
        "query": prompt,
        "resType": res_type,
        "status_code": 200,
        "has_response": has_response
    }

    return response


def generate_response_md(image_url, query):
    return generate_response_img(image_url, query)


def generate_mcq_res(prompt, temp_chat_history, que_type, user_query, has_image, language, temp_chat_history_app):
    if has_image:
        return generate_mcq_img_response(prompt, que_type, user_query, language)
    else:
        return generate_mcq_explanation(prompt, temp_chat_history, que_type, user_query, language, temp_chat_history_app)


def handle_tts_res(content):
    return handle_tts_data(content)


def get_res_id_from_ns(namespace):
    """
    Function to extract the resource ID from a namespace string.

    Args:
        namespace (str): The namespace string, e.g. "124_4424" or "877_245_4245"

    Returns:
        str: The last part of the namespace after splitting by underscore
    """
    if not namespace:
        return None

    parts = namespace.split('_')
    return parts[-1] if parts else None
